import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import {
  AppointmentRefundType,
  CheckoutItemType,
} from '../payment/sqldb/types';
import { refundCheckout } from '../payment/refund-checkout';
import { updateCheckout } from '../payment/update-checkout';
import { getCheckoutBalance } from '../payment/common';
import { Appointment } from './sqldb';
import { getAppointment } from './sqldb/queries';
import { AppointmentStatus } from './sqldb/types';

interface CancelAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
  refundAmount?: number;
  refundType?: AppointmentRefundType;
}

export async function cancelAppointment(
  params: CancelAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId, refundAmount = 0, refundType } = params;

  // lock the appointment

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment is temporarily locked',
      'cancel-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'cancel-appointment:appointment',
      );
    }

    if (appointment.status === AppointmentStatus.COMPLETED) {
      throw new ApolloError(
        'The appointment has already been completed',
        'cancel-appointment:completed',
      );
    }

    // TODO: send notifications

    try {
      await Appointment.query(sqldb.knex)
        .findById(appointmentId)
        .patch({ status: AppointmentStatus.CANCELLED });
    } catch (err) {
      throw new ApolloError(
        'Error canceling the appointment',
        'cancel-appointment:error',
      );
    }

    const cancelledAppointment = await getAppointment(sqldb.knex, {
      id: appointmentId,
    });

    if (cancelledAppointment && appointment.checkoutId) {
      let latestCheckout = await sqldb.checkout(appointment.checkoutId);
      if (!latestCheckout) throw new Error('Checkout not found');

      // Handle refund processing if needed
      if (refundAmount > 0 && refundType !== AppointmentRefundType.NONE) {
        console.log('[cancelAppointment] refunding checkout', refundAmount);
        const initialBalance = await getCheckoutBalance({
          sqldb,
          checkout: latestCheckout,
        });

        await refundCheckout({
          sqldb,
          checkoutId: appointment.checkoutId,
          refundAmount,
          balanceAfterRefund: initialBalance + refundAmount,
        });

        // Refresh checkout after refund
        latestCheckout = await sqldb.checkout(appointment.checkoutId);
        if (!latestCheckout) throw new Error('Checkout not found');
      }

      // Check final balance and add single "Appointment Cancelled" item if needed
      const finalBalance = await getCheckoutBalance({
        sqldb,
        checkout: latestCheckout,
      });

      console.log('[cancelAppointment] final balance', finalBalance);

      if (finalBalance !== 0) {
        const existingItems = latestCheckout.items ?? [];
        const cancelledItem = {
          type: CheckoutItemType.REFUND,
          quantity: 1,
          price: -finalBalance,
          description: 'Appointment Cancelled',
        };
        const newItems = [...existingItems, cancelledItem];

        console.log(
          '[cancelAppointment] adding appointment cancelled item to zero out balance',
          newItems,
        );
        await updateCheckout({
          sqldb,
          checkoutId: appointment.checkoutId,
          items: newItems,
        });
      }

      const updatedAppointment = await getAppointment(sqldb.knex, {
        id: appointmentId,
      });
      return updatedAppointment ?? cancelledAppointment;
    }

    return cancelledAppointment ?? null;
  } finally {
    await release();
  }
}
